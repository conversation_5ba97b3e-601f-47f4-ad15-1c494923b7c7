<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid #07c160;
        }
        .test-info h3 {
            margin-top: 0;
            color: #07c160;
        }
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .test-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 15px 20px;
            background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .test-btn:hover {
            background: linear-gradient(135deg, #06ad56 0%, #059c4f 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(7, 193, 96, 0.3);
        }
        .test-btn.secondary {
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        }
        .test-btn.secondary:hover {
            background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
            box-shadow: 0 6px 20px rgba(24, 144, 255, 0.3);
        }
        .instructions {
            background: #fff7e6;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #faad14;
        }
        .instructions h3 {
            margin-top: 0;
            color: #faad14;
        }
        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        .instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
        .status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: #f6ffed;
            border-radius: 8px;
            border-left: 4px solid #52c41a;
            margin-bottom: 20px;
        }
        .status-dot {
            width: 8px;
            height: 8px;
            background: #52c41a;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .feature {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .feature-icon {
            width: 20px;
            height: 20px;
            background: #07c160;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 实时通讯功能测试</h1>
        
        <div class="status">
            <div class="status-dot"></div>
            <span><strong>服务器状态：</strong>Socket.io 服务器运行在 localhost:3001</span>
        </div>

        <div class="test-info">
            <h3>📋 测试说明</h3>
            <p>本页面用于测试安全系统的实时通讯功能。您可以打开多个聊天窗口来模拟不同用户之间的对话。</p>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">💬</div>
                    <span>文本消息</span>
                </div>
                <div class="feature">
                    <div class="feature-icon">🖼️</div>
                    <span>图片消息</span>
                </div>
                <div class="feature">
                    <div class="feature-icon">🎤</div>
                    <span>语音输入</span>
                </div>
                <div class="feature">
                    <div class="feature-icon">😊</div>
                    <span>表情符号</span>
                </div>
                <div class="feature">
                    <div class="feature-icon">🔄</div>
                    <span>实时同步</span>
                </div>
                <div class="feature">
                    <div class="feature-icon">📱</div>
                    <span>响应式设计</span>
                </div>
            </div>
        </div>

        <div class="test-buttons">
            <a href="http://localhost:5174/chat" target="_blank" class="test-btn">
                <span>💬</span>
                打开聊天窗口 1
            </a>
            <a href="http://localhost:5174/chat" target="_blank" class="test-btn">
                <span>💬</span>
                打开聊天窗口 2
            </a>
            <a href="http://localhost:5174" target="_blank" class="test-btn secondary">
                <span>🏠</span>
                返回主页
            </a>
            <button onclick="openMultipleChats()" class="test-btn secondary">
                <span>🚀</span>
                一键打开多窗口
            </button>
        </div>

        <div class="instructions">
            <h3>🔧 测试步骤</h3>
            <ol>
                <li><strong>确保服务器运行：</strong>Socket.io 服务器应该在 localhost:3001 运行</li>
                <li><strong>打开多个聊天窗口：</strong>点击上方按钮打开2-3个聊天窗口</li>
                <li><strong>测试文本消息：</strong>在一个窗口发送文本消息，查看其他窗口是否实时接收</li>
                <li><strong>测试图片消息：</strong>点击图片按钮上传并发送图片</li>
                <li><strong>测试表情符号：</strong>点击表情按钮选择表情符号</li>
                <li><strong>测试语音模式：</strong>点击语音按钮切换到语音输入模式</li>
                <li><strong>观察连接状态：</strong>查看聊天窗口顶部的连接状态指示器</li>
            </ol>
        </div>
    </div>

    <script>
        function openMultipleChats() {
            // 打开3个聊天窗口用于测试
            for (let i = 1; i <= 3; i++) {
                setTimeout(() => {
                    window.open('http://localhost:5174/chat', `chat-window-${i}`, 'width=400,height=600,left=' + (i * 420) + ',top=100');
                }, i * 200);
            }
        }

        // 检查服务器状态
        async function checkServerStatus() {
            try {
                const response = await fetch('http://localhost:3001/health');
                const data = await response.json();
                console.log('服务器状态:', data);
            } catch (error) {
                console.warn('无法连接到聊天服务器:', error);
                const statusElement = document.querySelector('.status');
                statusElement.innerHTML = `
                    <div style="width: 8px; height: 8px; background: #ff4d4f; border-radius: 50%;"></div>
                    <span><strong>服务器状态：</strong>无法连接到 Socket.io 服务器 (localhost:3001)</span>
                `;
                statusElement.style.background = '#fff2f0';
                statusElement.style.borderLeftColor = '#ff4d4f';
            }
        }

        // 页面加载时检查服务器状态
        checkServerStatus();
    </script>
</body>
</html>
